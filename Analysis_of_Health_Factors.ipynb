import pandas as pd
import numpy as np
import seaborn as sns
import matplotlib.pyplot as plt
%matplotlib inline

diabetes_data = pd.read_csv("datasets\diabetes_BRFSS2015.csv")
diabetes_data.head()

diabetes_data.info()

# Plot Diabetes Status
sns.countplot(x='Diabetes', data=diabetes_data, hue='Diabetes')
plt.title('Diabetes Status')
plt.show()

print(diabetes_data['Diabetes'].value_counts(0))
print(diabetes_data['Diabetes'].value_counts(1))

# Removing individuals with prediabetes
diabetes_data = diabetes_data[diabetes_data['Diabetes'] != 'Prediabetes']

# Binary encoding 'Diabetes' column - {'No Diabetes': 0, 'Diabetes': 1}
diabetes_data['Diabetes'] = diabetes_data['Diabetes'].map({'No Diabetes': 0, 'Diabetes': 1})

# Plotting diabetes status by sex
sns.countplot(x='Sex', hue='Diabetes', data=diabetes_data)
plt.title('Diabetes Status by Sex')
plt.show()

# Calculate percentage of diabetes cases by sex
diabetes_by_sex = diabetes_data.groupby('Sex')['Diabetes'].mean()
print(diabetes_by_sex)

# Set the figure size
plt.figure(figsize=(10, 6))

# Plot Diabetes Status by Age Group
sns.countplot(x='Age', hue='Diabetes', data=diabetes_data, order=['18-24', '25-29', '30-34', '35-39', '40-44', '45-49', '50-54', '55-59', '60-64', '65-69', '70-74', '75-79', '80+'])
plt.title('Diabetes Status by Age Group')
plt.xticks(rotation=45)
plt.show()

# Calculate percentage of diabetes cases by age group
diabetes_by_age = diabetes_data.groupby('Age')['Diabetes'].mean()
print(diabetes_by_age)

# Plot Diabetes Status by High Blood Pressure
sns.countplot(x='HighBP', hue='Diabetes', data=diabetes_data)
plt.title('Diabetes Status by High Blood Pressure')
plt.show()

# Calculate percentage of diabetes cases by high blood pressure
diabetes_by_blood_pressure = diabetes_data.groupby('HighBP')['Diabetes'].mean()
print(diabetes_by_blood_pressure)

# Plot diabetes status by healthcare coverage
sns.countplot(x='AnyHealthcare', hue='Diabetes', data=diabetes_data)
plt.title('Diabetes Status by Healthcare Coverage')
plt.show()

# Calculate percentage of diabetes cases by healthcare coverage
print(diabetes_data['AnyHealthcare'].value_counts(1))
diabetes_data.groupby('AnyHealthcare')['Diabetes'].mean()

import matplotlib.pyplot as plt
import seaborn as sns

binary_features = ['HighChol', 'CholCheck', 'Smoker', 'Stroke', 'HeartDiseaseorAttack', 
                   'PhysActivity', 'Fruits', 'Veggies', 'HvyAlcoholConsump']

# Calculate percentages of diabetes by binary factor
diabetes_rates = {}
for feature in binary_features:
    rate = diabetes_data.groupby(feature)['Diabetes'].mean()[1] 
    diabetes_rates[feature] = rate

rate_df = pd.DataFrame(list(diabetes_rates.items()), columns=['Feature', 'DiabetesRate'])

# Plot Diabetes Rate by Binary Feature
plt.figure(figsize=(10,3))
sns.barplot(x='Feature', y='DiabetesRate', data=rate_df)
plt.title('Diabetes Rate by Binary Features')
plt.xticks(rotation=45)
plt.show()

# Print table of percentages
diabetes_percentage_table = pd.DataFrame.from_dict(diabetes_rates, orient='index', columns=['Diabetes Percentage'])
diabetes_percentage_table = diabetes_percentage_table.sort_values(by='Diabetes Percentage', ascending=False)
diabetes_percentage_table

# Plot BMI Distribution by Diabetes
fig, axes = plt.subplots(1, 2, figsize=(10, 5))

# Boxplot
sns.boxplot(x='Diabetes', y='BMI', data=diabetes_data, ax=axes[0])
axes[0].set_title('BMI Distribution by Diabetes (Boxplot)')
axes[0].set_xlabel('Diabetes')
axes[0].set_ylabel('BMI')

# KDE Plot
sns.kdeplot(data=diabetes_data, x='BMI', hue='Diabetes', fill=True, common_norm=False, ax=axes[1])
axes[1].set_title('BMI Distribution by Diabetes (KDE Plot)')
axes[1].set_xlabel('BMI')
axes[1].set_ylabel('Density')

plt.tight_layout()
plt.show()

# Print summary statistics by diabetes status
print("BMI Summary Statistics by Diabetes Status:\n", diabetes_data.groupby('Diabetes')['BMI'].describe())